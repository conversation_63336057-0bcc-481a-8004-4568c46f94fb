/**
 * FFMPEGVideoComposerModel
 * 
 * Concrete implementation of VideoToVideoModel using FFMPEG Docker service.
 * Handles video composition, overlay, and transformation operations.
 */

import { VideoToVideoModel, VideoCompositionOptions, VideoCompositionResult } from './VideoToVideoModel';
import { Video, VideoRole } from '../assets/roles';
import { VideoInput, castToVideo } from '../assets/casting';
import { ModelMetadata } from './Model';
import { FFMPEGDockerService } from '../services/FFMPEGDockerService';
import { FFMPEGAPIClient } from '../clients/FFMPEGAPIClient';

export interface FFMPEGVideoComposerConfig {
  dockerService: FFMPEGDockerService;
  apiClient: FFMPEGAPIClient;
}

/**
 * FFMPEG-based video composer model for overlay operations
 */
export class FFMPEGVideoComposerModel extends VideoToVideoModel {
  private dockerService: FFMPEGDockerService;
  private apiClient: FFMPEGAPIClient;

  constructor(config: FFMPEGVideoComposerConfig) {
    super({
      id: 'ffmpeg-video-composer',
      name: 'FFMPEG Video Composer',
      description: 'Video composition model using FFMPEG with overlay and positioning capabilities',
      version: '1.0.0',
      capabilities: [
        'video-overlay',
        'video-composition', 
        'smart-positioning',
        'aspect-ratio-handling',
        'multi-format-support'
      ],
      inputTypes: ['video'],
      outputTypes: ['video'],
      provider: 'ffmpeg-docker'
    });

    this.dockerService = config.dockerService;
    this.apiClient = config.apiClient;
  }

  /**
   * Transform and compose videos using FFMPEG
   */
  async transform(
    baseVideo: VideoInput,
    overlayVideo: VideoInput,
    options: VideoCompositionOptions = {}
  ): Promise<VideoCompositionResult> {
    // Cast inputs to Video roles
    const baseVideoRole = await castToVideo(baseVideo);
    const overlayVideoRole = await castToVideo(overlayVideo);

    // Set defaults
    const compositionOptions: Required<VideoCompositionOptions> = {
      overlayStartTime: options.overlayStartTime ?? 0,
      overlayDuration: options.overlayDuration ?? 0, // 0 means until end
      position: options.position ?? 'bottom-right',
      offsetX: options.offsetX ?? 0,
      offsetY: options.offsetY ?? 0,
      overlayWidth: options.overlayWidth ?? '25%',
      overlayHeight: options.overlayHeight ?? '25%',
      maintainAspectRatio: options.maintainAspectRatio ?? true,
      opacity: options.opacity ?? 1.0,
      blendMode: options.blendMode ?? 'normal',
      outputFormat: options.outputFormat ?? 'mp4',
      outputQuality: options.outputQuality ?? 'high',
      outputResolution: options.outputResolution ?? 'original',
      framerate: options.framerate ?? 30,
      smartPositioning: options.smartPositioning ?? true,
      fallbackPosition: options.fallbackPosition ?? 'bottom-center'
    };

    try {
      // Get video metadata to handle aspect ratios
      const baseMetadata = await this.getVideoMetadata(baseVideoRole);
      const overlayMetadata = await this.getVideoMetadata(overlayVideoRole);

      // Calculate smart positioning if enabled
      let finalPosition = compositionOptions.position;
      if (compositionOptions.smartPositioning) {
        const baseAspectRatio = baseMetadata.width / baseMetadata.height;
        const overlayAspectRatio = overlayMetadata.width / overlayMetadata.height;
        
        const safePosition = compositionOptions.position || 'bottom-right';
        const smartPosition = this.calculateSmartPosition(
          baseAspectRatio,
          overlayAspectRatio,
          safePosition,
          compositionOptions.fallbackPosition
        );
        finalPosition = smartPosition || 'bottom-right';
      }

      // Calculate overlay size
      const overlaySize = this.calculateOverlaySize(
        baseMetadata.width,
        baseMetadata.height,
        overlayMetadata.width,
        overlayMetadata.height,
        compositionOptions
      );

      // Calculate position coordinates
      const coordinates = this.calculatePositionCoordinates(
        baseMetadata.width,
        baseMetadata.height,
        overlaySize.width,
        overlaySize.height,
        finalPosition,
        compositionOptions.offsetX,
        compositionOptions.offsetY
      );      // Single-step process using colorkey for transparency
      const colorkeyOverlayFilter = this.buildColorkeyOverlayFilter(
        coordinates,
        overlaySize,
        compositionOptions
      );

      console.log('Generated colorkey overlay filter:', colorkeyOverlayFilter);
      console.log('Sending colorkey filter to API:', {
        outputFormat: compositionOptions.outputFormat,
        filterComplex: colorkeyOverlayFilter
      });

      // Call FFMPEG API for composition with colorkey filter
      const compositionResult = await this.apiClient.composeVideo(
        baseVideoRole.data,
        overlayVideoRole.data,
        {
          outputFormat: compositionOptions.outputFormat,
          filterComplex: colorkeyOverlayFilter
        }
      );

      // Create result Video asset
      if (!compositionResult.videoBuffer) {
        throw new Error('Video composition result missing video buffer');
      }
      
      const composedVideo = new Video(
        compositionResult.videoBuffer,
        baseVideoRole.sourceAsset
      );

      return {
        composedVideo,
        metadata: {
          duration: compositionResult.metadata.duration,
          resolution: `${compositionResult.metadata.width}x${compositionResult.metadata.height}`,
          aspectRatio: `${compositionResult.metadata.width}:${compositionResult.metadata.height}`,
          framerate: compositionResult.metadata.framerate,
          overlayInfo: {
            startTime: compositionOptions.overlayStartTime,
            duration: compositionOptions.overlayDuration,
            position: finalPosition,
            finalSize: overlaySize
          }
        }
      };

    } catch (error) {
      throw new Error(`Video composition failed: ${error.message}`);
    }
  }

  /**
   * Check if the model is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      return await this.dockerService.isHealthy();
    } catch (error) {
      console.warn('FFMPEG Video Composer availability check failed:', error.message);
      return false;
    }
  }

  /**
   * Get Docker service for advanced operations
   */
  getDockerService(): FFMPEGDockerService {
    return this.dockerService;
  }

  /**
   * Get API client for advanced operations
   */
  getAPIClient(): FFMPEGAPIClient {
    return this.apiClient;
  }  /**
   * Get video metadata using FFMPEG
   */
  private async getVideoMetadata(video: Video): Promise<{
    width: number;
    height: number;
    duration: number;
    framerate: number;
    aspectRatio: number;
  }> {
    try {
        console.log('Requesting video metadata from API...');
      const metadata = await this.apiClient.getVideoMetadata(video.data);
      console.log('Video metadata from API:', metadata);
      
      // Store the full API metadata in the video object for later use
      (video as any).apiMetadata = metadata;
      
      return {
        width: metadata.width,
        height: metadata.height,
        duration: metadata.duration,
        framerate: metadata.framerate,
        aspectRatio: metadata.width / metadata.height
      };
    } catch (error) {
      // Fallback to basic metadata if available
      if (video.metadata?.width && video.metadata?.height) {
        return {
          width: video.metadata.width,
          height: video.metadata.height,
          duration: video.metadata.duration || 0,
          framerate: video.metadata.framerate || 30,
          aspectRatio: video.metadata.width / video.metadata.height
        };
      }
      throw new Error(`Could not get video metadata: ${error.message}`);
    }
  }

  /**
   * Calculate position coordinates for overlay
   */
  private calculatePositionCoordinates(
    baseWidth: number,
    baseHeight: number,
    overlayWidth: number,
    overlayHeight: number,
    position: VideoCompositionOptions['position'],
    offsetX: number,
    offsetY: number
  ): { x: number; y: number } {
    let x = 0;
    let y = 0;

    // Calculate base position
    switch (position) {
      case 'top-left':
        x = 0;
        y = 0;
        break;
      case 'top-center':
        x = (baseWidth - overlayWidth) / 2;
        y = 0;
        break;
      case 'top-right':
        x = baseWidth - overlayWidth;
        y = 0;
        break;
      case 'center-left':
        x = 0;
        y = (baseHeight - overlayHeight) / 2;
        break;
      case 'center':
        x = (baseWidth - overlayWidth) / 2;
        y = (baseHeight - overlayHeight) / 2;
        break;
      case 'center-right':
        x = baseWidth - overlayWidth;
        y = (baseHeight - overlayHeight) / 2;
        break;
      case 'bottom-left':
        x = 0;
        y = baseHeight - overlayHeight;
        break;
      case 'bottom-center':
        x = (baseWidth - overlayWidth) / 2;
        y = baseHeight - overlayHeight;
        break;
      case 'bottom-right':
      default:
        x = baseWidth - overlayWidth;
        y = baseHeight - overlayHeight;
        break;
    }

    // Apply offsets
    x += offsetX;
    y += offsetY;

    // Ensure coordinates are within bounds
    x = Math.max(0, Math.min(x, baseWidth - overlayWidth));
    y = Math.max(0, Math.min(y, baseHeight - overlayHeight));

    return { x: Math.round(x), y: Math.round(y) };
  }

  /**
   * Build colorkey overlay filter for single-step transparency
   */
  private buildColorkeyOverlayFilter(
    coordinates: { x: number; y: number },
    overlaySize: { width: number; height: number },
    options: Required<VideoCompositionOptions>
  ): string {
    // Build the overlay processing chain
    let overlayChain = `[1:v]`;

    // Add timing trim if overlay should start at a specific time
    if (options.overlayStartTime > 0 || options.overlayDuration > 0) {
      // Trim overlay to show from its beginning, with proper duration
      if (options.overlayDuration > 0) {
        overlayChain += `trim=start=0:duration=${options.overlayDuration},setpts=PTS-STARTPTS,`;
      } else {
        overlayChain += `trim=start=0,setpts=PTS-STARTPTS,`;
      }
    }

    // Apply colorkey and scaling
    overlayChain += `colorkey=0x000000:0.30:0.10,scale=${overlaySize.width}:${overlaySize.height}`;

    // Apply opacity if needed
    if (options.opacity < 1.0) {
      overlayChain += `,format=rgba,colorchannelmixer=aa=${options.opacity}`;
    }

    overlayChain += `[ov]`;

    // Build the overlay composition
    let overlayFilter = `${overlayChain};[0:v][ov]overlay=format=auto:x=${coordinates.x}:y=${coordinates.y}`;

    // Add timing delay for when overlay should appear
    if (options.overlayStartTime > 0) {
      if (options.overlayDuration > 0) {
        const endTime = options.overlayStartTime + options.overlayDuration;
        overlayFilter += `:enable='between(t,${options.overlayStartTime},${endTime})'`;
      } else {
        overlayFilter += `:enable='gte(t,${options.overlayStartTime})'`;
      }
    }

    overlayFilter += '[v]';

    return overlayFilter;
  }



}
